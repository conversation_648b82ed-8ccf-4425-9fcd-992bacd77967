import {blog} from '@/lib/blog'
import Link from "next/link";
import Image from "next/image";

export default function BlogPage() {
  const posts = blog.getSorted()

  return (
    <div className="flex flex-col flex-1 gap-8 w-full max-w-2xl">
      {posts.map(post => (
        <article key={post.slug} className="bg-card text-card-foreground rounded-xl border shadow-sm">
          <Link href={post.permalink} className="grid grid-cols-[60%_40%] gap-0 w-full h-full">
            <div className="flex flex-col p-4 justify-between h-[140px]">
              <header className="flex-1 text-[1rem] font-bold">
                <span className="line-clamp-2 mb-0 leading-[1.25rem]">
                  { post.title }
                </span>
                {post.title.length <= 50 && post.description && (
                  <span className="text-sm font-normal text-muted-foreground pt-2 line-clamp-2 overflow-hidden">
                    { post.excerpt }
                  </span>
                )}
              </header>
              <footer className="text-[0.75rem] font-bold text-muted-foreground leading-[0.75rem] ">
                { new Date(post.date).toLocaleDateString(undefined, {year: 'numeric', month: 'long'}) }
              </footer>
            </div>
            <div className="col-start-2 max-h-full p-0 m-0 [clip-path:polygon(10%_0%,100%_0%,100%_100%,0%_100%)]">
              <picture className="flex justify-center m-0 select-none">
                { post.cover && <Image src={post.cover} alt={post.title} placeholder="blur" className="w-full h-[140px] block object-cover rounded-r-xl"/> }
              </picture>
            </div>
          </Link>
        </article>
      ))}
    </div>
  )
}