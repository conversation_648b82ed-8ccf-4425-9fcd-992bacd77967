import * as runtime from 'react/jsx-runtime'
import React from "react";

import { Blockquote } from "@/components/posts/blockquote";
import { Button } from "@/components/posts/button";
import NextLink from "next/link";

export const sharedComponents = {
  a: (props: React.AnchorHTMLAttributes<HTMLAnchorElement>) => (
    <Button variant="link" asChild className="px-0" {...props}>
      <NextLink href={href || "#"}>
        {children}
      </NextLink>
    </Button>
  ),
  Blockquote,
  Button
}

const useMDXComponent = (code: string) => {
  const fn = new Function(code)
  return fn({ ...runtime }).default
}

interface MDXProps {
  code: string
  components?: Record<string, React.ComponentType>
}

export const MDXContent = ({ code, components }: MDXProps) => {
  const Component = useMDXComponent(code)
  return <Component components={{ ...sharedComponents, ...components }} />
}
