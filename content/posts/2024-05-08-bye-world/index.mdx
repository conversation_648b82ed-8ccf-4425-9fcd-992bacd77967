---
title: The easiest way to render an outline in Unity
slug: bye-world
description: I shit my pants twice in the last 4 hours
introduction: Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed non risus. Suspendisse lobortis nulla id turpis.
date: 2025-07-17 13:00:00
cover: cover.jpg
categories:
  - Journal
---

<h1 class="text-2xl font-bold">Introduction</h1>
Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed non risus. Suspendisse
lobortis nulla id turpis. In condimentum. In hac habitasse platea dictumst. Nulla
non lectus sed nisl molestie malesuada. Nulla sit amet metus. Nulla sit amet
metus. Nulla sit amet metus. Nulla sit amet metus. Nulla sit amet metus.

<br />
[Link to plain.txt](plain.txt)

<Button variant="default">Button</Button>

<Button variant="destructive">Button</Button>

<Button variant="outline">Button</Button>

<Button variant="secondary">Button</Button>

<Button variant="ghost">Button</Button>

<Button variant="link">Button</Button>

<Blockquote type="info">This is an info</Blockquote>
<Blockquote type="question">This is a question</Blockquote>
<Blockquote type="success">This is a success</Blockquote>
<Blockquote type="warning">This is a warning</Blockquote>
<Blockquote type="error">This is an error</Blockquote>
